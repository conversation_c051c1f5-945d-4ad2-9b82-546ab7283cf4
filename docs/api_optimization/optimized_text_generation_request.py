"""
优化后的文本生成请求参数设计

主要改进：
1. 更清晰的参数分组和层次结构
2. 更合理的默认值设置
3. 增强的参数验证
4. 更好的文档说明
"""

from enum import Enum
from typing import Any, Dict, List, Optional, Union
from pydantic import BaseModel, Field, validator


class MessageRole(str, Enum):
    """消息角色枚举"""
    SYSTEM = "system"
    USER = "user"
    ASSISTANT = "assistant"
    FUNCTION = "function"
    TOOL = "tool"


class Message(BaseModel):
    """聊天消息模型"""
    role: MessageRole = Field(..., description="消息角色")
    content: str = Field(..., min_length=1, description="消息内容，不能为空")
    name: Optional[str] = Field(None, description="消息发送者名称")
    function_call: Optional[Dict[str, Any]] = Field(None, description="函数调用信息")
    tool_calls: Optional[List[Dict[str, Any]]] = Field(None, description="工具调用信息")


class GenerationConfig(BaseModel):
    """生成配置参数组"""
    max_tokens: Optional[int] = Field(
        None, 
        ge=1, 
        le=32768, 
        description="最大生成token数，不设置则使用模型默认值"
    )
    temperature: float = Field(
        0.7, 
        ge=0.0, 
        le=2.0, 
        description="生成温度，控制随机性。0.0为确定性输出，2.0为最大随机性"
    )
    top_p: Optional[float] = Field(
        None, 
        ge=0.0, 
        le=1.0, 
        description="核采样参数，与temperature互斥使用"
    )
    top_k: Optional[int] = Field(
        None, 
        ge=1, 
        le=100, 
        description="Top-K采样参数，限制候选token数量"
    )
    frequency_penalty: float = Field(
        0.0, 
        ge=-2.0, 
        le=2.0, 
        description="频率惩罚，减少重复内容"
    )
    presence_penalty: float = Field(
        0.0, 
        ge=-2.0, 
        le=2.0, 
        description="存在惩罚，鼓励话题多样性"
    )
    stop: Optional[Union[str, List[str]]] = Field(
        None, 
        description="停止序列，遇到时停止生成"
    )

    @validator('stop')
    def validate_stop_sequences(cls, v):
        """验证停止序列"""
        if v is None:
            return v
        if isinstance(v, str):
            return [v] if v.strip() else None
        if isinstance(v, list):
            # 过滤空字符串并限制数量
            filtered = [s for s in v if isinstance(s, str) and s.strip()]
            if len(filtered) > 10:
                raise ValueError("停止序列数量不能超过10个")
            return filtered if filtered else None
        raise ValueError("停止序列必须是字符串或字符串列表")


class StreamConfig(BaseModel):
    """流式输出配置"""
    enabled: bool = Field(False, description="是否启用流式输出")
    chunk_size: Optional[int] = Field(
        None, 
        ge=1, 
        le=1000, 
        description="流式输出块大小（字符数）"
    )
    include_usage: bool = Field(
        True, 
        description="是否在流式输出中包含使用量统计"
    )


class SafetyConfig(BaseModel):
    """安全配置参数"""
    content_filter: bool = Field(True, description="是否启用内容过滤")
    safety_level: str = Field(
        "medium", 
        regex="^(low|medium|high|strict)$",
        description="安全级别：low, medium, high, strict"
    )
    custom_filters: Optional[List[str]] = Field(
        None, 
        description="自定义过滤规则"
    )


class OptimizedTextGenerationRequest(BaseModel):
    """优化后的文本生成请求模型
    
    主要改进：
    1. 参数分组：将相关参数组织到子配置中
    2. 更好的默认值：基于最佳实践设置合理默认值
    3. 增强验证：添加更严格的参数验证
    4. 清晰文档：提供详细的参数说明
    """
    
    # === 核心必需参数 ===
    messages: List[Message] = Field(
        ..., 
        min_items=1, 
        description="对话消息列表，至少包含一条消息"
    )
    model: str = Field(
        ..., 
        min_length=1, 
        description="使用的模型名称，如 'gpt-4', 'claude-3-sonnet'"
    )
    
    # === 生成配置 ===
    generation: GenerationConfig = Field(
        default_factory=GenerationConfig,
        description="文本生成配置参数"
    )
    
    # === 流式配置 ===
    stream: StreamConfig = Field(
        default_factory=StreamConfig,
        description="流式输出配置"
    )
    
    # === 安全配置 ===
    safety: Optional[SafetyConfig] = Field(
        None,
        description="安全配置，不设置则使用默认安全策略"
    )
    
    # === 高级功能参数 ===
    functions: Optional[List[Dict[str, Any]]] = Field(
        None, 
        description="可用函数列表，用于函数调用功能"
    )
    function_call: Optional[Union[str, Dict[str, str]]] = Field(
        None, 
        description="函数调用设置：'auto', 'none', 或指定函数"
    )
    tools: Optional[List[Dict[str, Any]]] = Field(
        None, 
        description="可用工具列表"
    )
    tool_choice: Optional[Union[str, Dict[str, Any]]] = Field(
        None, 
        description="工具选择设置"
    )
    
    # === 结构化输出参数 ===
    response_format: Optional[Dict[str, Any]] = Field(
        None, 
        description="响应格式配置，支持JSON Schema约束"
    )
    response_schema: Optional[Dict[str, Any]] = Field(
        None, 
        description="JSON Schema约束"
    )
    
    # === 元数据参数 ===
    user: Optional[str] = Field(
        None, 
        description="用户标识，用于追踪和分析"
    )
    metadata: Optional[Dict[str, Any]] = Field(
        None, 
        description="请求元数据，用于日志和分析"
    )
    
    # === 供应商特定参数 ===
    provider_params: Dict[str, Any] = Field(
        default_factory=dict, 
        description="供应商特定参数，透传给底层API"
    )

    @validator('messages')
    def validate_messages(cls, v):
        """验证消息列表"""
        if not v:
            raise ValueError("消息列表不能为空")
        
        # 检查消息角色顺序的合理性
        roles = [msg.role for msg in v]
        
        # 第一条消息通常应该是system或user
        if roles[0] not in [MessageRole.SYSTEM, MessageRole.USER]:
            raise ValueError("第一条消息应该是system或user角色")
        
        # 检查是否有连续的相同角色（除了system）
        for i in range(1, len(roles)):
            if roles[i] == roles[i-1] and roles[i] != MessageRole.SYSTEM:
                # 允许连续的system消息，但警告其他情况
                pass
        
        return v

    @validator('model')
    def validate_model(cls, v):
        """验证模型名称"""
        if not v or not v.strip():
            raise ValueError("模型名称不能为空")
        
        # 可以添加支持的模型列表验证
        # supported_models = ["gpt-4", "gpt-3.5-turbo", "claude-3-sonnet", ...]
        # if v not in supported_models:
        #     raise ValueError(f"不支持的模型: {v}")
        
        return v.strip()

    @validator('generation')
    def validate_generation_config(cls, v):
        """验证生成配置"""
        # 检查temperature和top_p是否同时设置
        if v.temperature != 0.7 and v.top_p is not None:
            # 警告：建议只使用其中一个
            pass
        
        return v

    class Config:
        """Pydantic配置"""
        # 允许额外字段，但会发出警告
        extra = "forbid"
        # 使用枚举值而不是枚举名称
        use_enum_values = True
        # 验证赋值
        validate_assignment = True
        # JSON编码器
        json_encoders = {
            # 可以添加自定义编码器
        }


# === 向后兼容的简化版本 ===
class SimpleTextGenerationRequest(BaseModel):
    """简化版本的文本生成请求，保持向后兼容性"""
    
    messages: List[Message] = Field(..., description="对话消息列表")
    model: str = Field(..., description="使用的模型名称")
    max_tokens: Optional[int] = Field(None, description="最大生成token数")
    temperature: Optional[float] = Field(0.7, ge=0.0, le=2.0, description="生成温度")
    stream: bool = Field(False, description="是否流式输出")
    
    def to_optimized(self) -> OptimizedTextGenerationRequest:
        """转换为优化版本的请求"""
        generation_config = GenerationConfig(
            max_tokens=self.max_tokens,
            temperature=self.temperature or 0.7
        )
        
        stream_config = StreamConfig(enabled=self.stream)
        
        return OptimizedTextGenerationRequest(
            messages=self.messages,
            model=self.model,
            generation=generation_config,
            stream=stream_config
        )
